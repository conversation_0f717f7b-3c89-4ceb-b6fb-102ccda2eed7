#!/usr/bin/env python3
"""
Test the enhanced prompt cleaning system to ensure proper formatting
"""

def test_prompt_cleaning():
    """Test the enhanced prompt cleaning functionality"""
    
    def clean_ai_response(response_text, topic, icon_style, grid_type):
        """Simulate the enhanced cleaning function"""
        
        prompt = response_text.strip()
        
        # Remove extensive list of unwanted intro phrases and formatting
        unwanted_phrases = [
            "Here's a detailed prompt:", "Here is the prompt:", "Prompt:", 
            "Here's the icon set prompt:", "Generated prompt:", "Icon set prompt:",
            "Here is a complete and detailed prompt", "Here's a complete prompt",
            "**Prompt:**", "**Generated Prompt:**", "**Icon Set Prompt:**",
            "A set of Here is a", "Here is a", "Generate a professional",
            "**Top Row", "**Middle Row", "**Bottom Row",
            "Following the requirements", "Based on your requirements",
            "Here's the complete prompt", "Complete prompt:",
            "The prompt is:", "Final prompt:"
        ]
        
        # Clean unwanted intro text
        for phrase in unwanted_phrases:
            if phrase.lower() in prompt.lower():
                # Find and remove the phrase and everything before it if it's at the start
                phrase_pos = prompt.lower().find(phrase.lower())
                if phrase_pos < 50:  # If phrase is near the beginning
                    prompt = prompt[phrase_pos + len(phrase):].strip()
        
        # Remove markdown formatting
        prompt = prompt.replace("**", "").replace("*", "")
        
        # Remove any remaining intro patterns
        lines = prompt.split('\n')
        clean_lines = []
        found_start = False
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Look for the actual start of the prompt
            if line.lower().startswith("a set of nine") or line.lower().startswith("a set of"):
                found_start = True
                clean_lines.append(line)
            elif found_start:
                clean_lines.append(line)
        
        # Join the clean lines
        if clean_lines:
            prompt = " ".join(clean_lines)
        
        # Ensure it starts properly
        if not prompt.lower().startswith("a set of"):
            # Try to find "A set of" anywhere in the text
            set_pos = prompt.lower().find("a set of")
            if set_pos > 0:
                prompt = prompt[set_pos:]
            else:
                prompt = f"A set of nine {icon_style} {topic} icons arranged in a {grid_type}. " + prompt
        
        return prompt
    
    # Test cases with problematic AI responses
    test_cases = [
        {
            "name": "Justice Icons - Problematic Response",
            "topic": "justice",
            "style": "minimalist",
            "grid": "3x3 grid",
            "response": """A set of Here is a complete and detailed prompt for generating a set of 9 Justice and Court icons in a 3x3 grid, adhering to your specific requirements: 

**Prompt:**

A set of nine minimalist justice icons arranged in a 3x3 grid. The top row features three solid black designs – a balanced scale on the left, a judge's gavel in the middle, and a courthouse building on the right. The middle row shows three detailed variations – the scale includes intricate balance mechanisms and weight details, the gavel features wood grain texture and metal band highlights, and the courthouse displays classical columns with architectural details. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, minimalist style against a white background, with balanced spacing and crisp line work. The set represents law, order, and judicial authority with clarity and professionalism."""
        },
        {
            "name": "Technology Icons - Clean Response",
            "topic": "technology", 
            "style": "minimalist",
            "grid": "3x3 grid",
            "response": """A set of nine minimalist technology icons arranged in a 3x3 grid. The top row features three solid black designs – a smartphone with app grid display on the left, a cloud server with data streams in the middle, and a robotic gear mechanism on the right. The middle row shows three detailed variations – the smartphone includes screen reflection and button details, the cloud features flowing data particles and server rack elements, and the gear displays interlocking teeth and mechanical precision. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, minimalist style against a white background, with balanced spacing and sharp line work. The set represents innovation, connectivity, and digital transformation with clarity and sophistication."""
        },
        {
            "name": "Finance Icons - Multiple Issues",
            "topic": "finance",
            "style": "minimalist", 
            "grid": "3x3 grid",
            "response": """Here's a complete prompt for your finance icon set:

**Generated Prompt:**

**Top Row (3 solid black designs):**
A set of nine minimalist finance icons arranged in a 3x3 grid. The top row features three solid black designs – a dollar sign with bold strokes on the left, a piggy bank silhouette in the middle, and a bar chart with ascending bars on the right. The middle row shows three detailed variations – the dollar sign includes subtle shading and dimensional depth, the piggy bank features coin slot details and curved surface highlights, and the bar chart displays grid lines and data point markers. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, minimalist style against a white background, with balanced spacing and precise line work. The set symbolizes wealth, savings, and financial growth with clarity and trust."""
        }
    ]
    
    print("🧹 Enhanced Prompt Cleaning Test")
    print("=" * 60)
    print("🎯 Testing removal of unwanted intro text and formatting")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test {i}: {test_case['name']}")
        print("-" * 50)
        
        original = test_case['response']
        cleaned = clean_ai_response(original, test_case['topic'], test_case['style'], test_case['grid'])
        
        print(f"📄 Original Length: {len(original)} characters")
        print(f"🧹 Cleaned Length: {len(cleaned)} characters")
        
        # Check for problematic elements
        problems = []
        if "Here is a" in original:
            problems.append("❌ 'Here is a' intro text")
        if "**Prompt:**" in original:
            problems.append("❌ '**Prompt:**' formatting")
        if "**Top Row" in original:
            problems.append("❌ Markdown section headers")
        
        if problems:
            print(f"🚨 Original Issues Found:")
            for problem in problems:
                print(f"   {problem}")
        else:
            print(f"✅ Original was clean")
        
        # Check if cleaning worked
        clean_check = []
        if cleaned.startswith("A set of nine"):
            clean_check.append("✅ Starts correctly")
        else:
            clean_check.append("❌ Wrong start")
            
        if "Here is a" not in cleaned:
            clean_check.append("✅ No intro text")
        else:
            clean_check.append("❌ Still has intro text")
            
        if "**" not in cleaned:
            clean_check.append("✅ No markdown formatting")
        else:
            clean_check.append("❌ Still has markdown")
        
        print(f"🧹 Cleaning Results:")
        for check in clean_check:
            print(f"   {check}")
        
        # Show preview
        preview = cleaned[:150] + "..." if len(cleaned) > 150 else cleaned
        print(f"📝 Cleaned Preview: {preview}")
        print("-" * 50)
    
    # Test the target format
    print(f"\n🎯 Target Format Validation")
    print("=" * 60)
    
    target_format = """A set of nine minimalist organic vegan icons arranged in a 3x3 grid. The top row features three solid black designs – a sprouting leaf on the left, a carrot with foliage in the middle, and a circular "V" symbol for vegan on the right. The middle row shows three detailed variations – the leaf includes subtle veins and a curved stem, the carrot features texture lines and lush greenery, and the vegan symbol is encircled with a decorative wreath. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, minimalist style against a white background, with balanced spacing and refined line work. The set represents plant-based lifestyle, sustainability, and natural health with clarity and organic simplicity."""
    
    print("✅ Target Format Example:")
    print(f"   • Starts with: 'A set of nine minimalist...'")
    print(f"   • No intro text or formatting")
    print(f"   • Complete structure: top row → middle row → bottom row → conclusion")
    print(f"   • Length: {len(target_format)} characters")
    print(f"   • Professional and clean")
    
    print(f"\n🚀 Enhanced Cleaning Features:")
    features = [
        "✅ Removes 15+ types of unwanted intro phrases",
        "✅ Strips all markdown formatting (**text**)",
        "✅ Line-by-line cleaning for complex responses", 
        "✅ Finds and preserves 'A set of' start anywhere in text",
        "✅ Reconstructs proper format if needed",
        "✅ Maintains complete icon descriptions",
        "✅ Preserves professional conclusion"
    ]
    
    for feature in features:
        print(f"   {feature}")

if __name__ == "__main__":
    test_prompt_cleaning()
