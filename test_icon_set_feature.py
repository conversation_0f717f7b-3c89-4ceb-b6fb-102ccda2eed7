#!/usr/bin/env python3
"""
Test script for the new Icon Set Custom Prompt Generator feature
This script demonstrates the new functionality added to Meta Master.py
"""

def test_icon_set_prompt_generation():
    """Test the icon set prompt generation function"""
    
    # Import the function from Meta Master (this would work if we could import it)
    # For now, we'll recreate the function here for testing
    
    def generate_icon_set_prompt_text(topic, icon_count, grid_type, icon_style):
        """Generate the actual prompt text for icon sets based on parameters."""
        
        # Map icon count to appropriate grid if not specified
        count_to_grid_map = {
            "9": "3x3 grid",
            "16": "4x4 grid", 
            "24": "4x6 grid",
            "30": "5x6 grid",
            "48": "6x8 grid"
        }
        
        # Use specified grid or map from count
        if grid_type == "auto":
            grid_layout = count_to_grid_map.get(icon_count, "3x3 grid")
        else:
            grid_layout = grid_type
        
        # Style descriptions
        style_descriptions = {
            "minimalist": "minimalist",
            "solid black": "solid black designs",
            "outlined": "outlined style with clean line work",
            "filled": "filled designs with solid colors",
            "gradient": "gradient-filled designs",
            "flat design": "flat design style",
            "line art": "line-drawn style",
            "detailed": "detailed designs with textures and shading"
        }
        
        style_desc = style_descriptions.get(icon_style, "minimalist")
        
        # Generate the prompt following the pattern from your samples
        if icon_count == "9":
            prompt = f"""A set of nine {style_desc} {topic} icons arranged in a {grid_layout}. The top row features three solid designs – a primary {topic} symbol on the left, a secondary {topic} element in the middle, and a tertiary {topic} icon on the right. The middle row shows three variations – a detailed {topic} component on the left, a {topic} tool or device in the center, and a {topic} action or process on the right. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, {style_desc} style against a white background, with balanced spacing and precise line work. The designs convey {topic} concepts with clarity and simplicity."""
        
        elif icon_count == "16":
            prompt = f"""A set of sixteen {style_desc} {topic} icons arranged in a {grid_layout}. The design includes four rows of four icons each, covering comprehensive {topic} concepts. The first row features primary {topic} symbols, the second row shows {topic} tools and equipment, the third row displays {topic} processes and actions, and the fourth row contains {topic} results and outcomes. All icons maintain consistent {style_desc} styling with uniform line weights, balanced proportions, and cohesive visual treatment. Rendered against a white background with professional spacing and clarity."""
        
        elif icon_count == "24":
            prompt = f"""A comprehensive set of twenty-four {style_desc} {topic} icons arranged in a {grid_layout}. The collection covers all essential {topic} concepts organized in systematic rows. Each row represents a different aspect of {topic}: fundamental concepts, tools and equipment, processes and methods, applications and uses, results and outcomes, and advanced concepts. All icons feature consistent {style_desc} design with uniform styling, balanced proportions, and professional quality. Clean white background with precise spacing and modern aesthetic."""
        
        elif icon_count == "30":
            prompt = f"""An extensive set of thirty {style_desc} {topic} icons arranged in a {grid_layout}. This comprehensive collection covers the complete spectrum of {topic} concepts, tools, processes, and applications. Icons are organized in systematic rows covering: basic {topic} elements, intermediate concepts, advanced applications, tools and equipment, processes and methods, and specialized {topic} areas. All icons maintain strict {style_desc} consistency with uniform line weights, balanced proportions, and cohesive visual language. Professional white background with optimal spacing."""
        
        elif icon_count == "48":
            prompt = f"""A complete professional set of forty-eight {style_desc} {topic} icons arranged in a {grid_layout}. This comprehensive icon library covers every aspect of {topic} including: fundamental concepts, basic tools, intermediate equipment, advanced systems, processes and workflows, applications and uses, results and metrics, specialized areas, industry standards, and emerging trends. All icons feature consistent {style_desc} design with professional quality, uniform styling, balanced proportions, and cohesive visual treatment. Clean white background with precise grid alignment and optimal spacing for maximum clarity and usability."""
        
        else:
            # Fallback for other counts
            prompt = f"""A set of {icon_count} {style_desc} {topic} icons arranged in a {grid_layout}. The collection comprehensively covers {topic} concepts, tools, processes, and applications. All icons feature consistent {style_desc} design with uniform styling, balanced proportions, and professional quality. Clean white background with precise spacing and modern aesthetic that conveys {topic} concepts with clarity and simplicity."""
        
        return prompt

    # Test cases based on your examples
    test_cases = [
        {
            "topic": "electricity",
            "icon_count": "9",
            "grid_type": "3x3 grid",
            "icon_style": "minimalist"
        },
        {
            "topic": "fitness",
            "icon_count": "9", 
            "grid_type": "3x3 grid",
            "icon_style": "solid black"
        },
        {
            "topic": "investment",
            "icon_count": "9",
            "grid_type": "3x3 grid", 
            "icon_style": "minimalist"
        },
        {
            "topic": "technology",
            "icon_count": "16",
            "grid_type": "4x4 grid",
            "icon_style": "outlined"
        },
        {
            "topic": "business",
            "icon_count": "24",
            "grid_type": "4x6 grid",
            "icon_style": "flat design"
        }
    ]
    
    print("🔲 Icon Set Custom Prompt Generator Test Results")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}: {test_case['topic'].title()} Icons")
        print(f"   Count: {test_case['icon_count']} | Grid: {test_case['grid_type']} | Style: {test_case['icon_style']}")
        print("-" * 60)
        
        prompt = generate_icon_set_prompt_text(
            test_case['topic'],
            test_case['icon_count'], 
            test_case['grid_type'],
            test_case['icon_style']
        )
        
        print(f"Generated Prompt:\n{prompt}")
        print("-" * 60)

if __name__ == "__main__":
    test_icon_set_prompt_generation()
