#!/usr/bin/env python3
"""
Test the enhanced icon set prompt generation with improved patterns
"""

def test_enhanced_icon_generation():
    """Test the enhanced icon generation with topic-specific details"""
    
    # Import the helper functions (simplified versions for testing)
    def get_topic_specific_icons(topic):
        """Get specific icon descriptions based on topic from the sample patterns."""
        topic_icons_db = {
            "fitness": [
                {"desc": "a dumbbell", "detail": "the dumbbell includes grip texture and shading"},
                {"desc": "a running shoe", "detail": "the running shoe features laces and sole details"},
                {"desc": "a heart rate monitor", "detail": "the heart rate monitor displays pulse lines and screen details"}
            ],
            "food": [
                {"desc": "an apple", "detail": "the apple includes leaf veins and shading"},
                {"desc": "a fork and knife crossed", "detail": "the fork and knife display handle textures and reflections"},
                {"desc": "a bowl of soup", "detail": "the bowl of soup features steam lines and soup surface details"}
            ],
            "medicine": [
                {"desc": "a pill capsule", "detail": "the pill capsule includes dotted texture and shading"},
                {"desc": "a medicine bottle with a cross label", "detail": "the medicine bottle displays label details and highlights"},
                {"desc": "a stethoscope", "detail": "the stethoscope features tubing curves and earpiece details"}
            ]
        }
        
        if topic in topic_icons_db:
            return topic_icons_db[topic]
        return None

    def get_line_work_desc(style_desc):
        """Get appropriate line work description based on style."""
        line_work_map = {
            "minimalist": "crisp line work",
            "solid black designs": "refined line work", 
            "outlined style with clean line work": "precise line work"
        }
        return line_work_map.get(style_desc, "crisp line work")

    def get_topic_conclusion(topic):
        """Get appropriate conclusion phrase based on topic."""
        topic_conclusions = {
            "fitness": "represents strength, endurance, and health",
            "food": "represents nutrition, dining, and culinary arts",
            "medicine": "symbolizes health, treatment, and medical care"
        }
        return topic_conclusions.get(topic.lower(), f"represents {topic} concepts and applications")

    def get_style_conclusion(topic):
        """Get appropriate style conclusion based on topic."""
        style_conclusions = {
            "fitness": "energy",
            "food": "simplicity",
            "medicine": "professionalism"
        }
        return style_conclusions.get(topic.lower(), "professionalism")

    def generate_enhanced_icon_prompt(topic, icon_count="9", grid_type="3x3 grid", icon_style="minimalist"):
        """Generate enhanced icon set prompt with topic-specific details."""
        
        style_desc = icon_style
        topic_icons = get_topic_specific_icons(topic.lower())
        
        if icon_count == "9" and topic_icons:
            icon1, icon2, icon3 = topic_icons[:3]
            prompt = f"""A set of nine {style_desc} {topic} icons arranged in a {grid_type}. The top row features three solid black designs – {icon1['desc']} on the left, {icon2['desc']} in the middle, and {icon3['desc']} on the right. The middle row shows three detailed variations – {icon1['detail']}, {icon2['detail']}, and {icon3['detail']}. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, {style_desc} style against a white background, with balanced spacing and {get_line_work_desc(style_desc)}. The set {get_topic_conclusion(topic)} with clarity and {get_style_conclusion(topic)}."""
        else:
            # Fallback for unknown topics
            prompt = f"""A set of nine {style_desc} {topic} icons arranged in a {grid_type}. The top row features three solid black designs – a primary {topic} symbol on the left, a secondary {topic} element in the middle, and a tertiary {topic} icon on the right. The middle row shows three detailed variations – the first icon includes enhanced details and shading, the second features additional elements and textures, and the third displays refined characteristics and depth. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, {style_desc} style against a white background, with balanced spacing and {get_line_work_desc(style_desc)}. The set {get_topic_conclusion(topic)} with clarity and {get_style_conclusion(topic)}."""
        
        return prompt

    # Test cases
    test_topics = ["fitness", "food", "medicine", "technology", "business"]
    
    print("🔲 Enhanced Icon Set Prompt Generation Test")
    print("=" * 70)
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n📋 Test {i}: {topic.title()} Icons")
        print("-" * 50)
        
        prompt = generate_enhanced_icon_prompt(topic)
        
        # Show the first 300 characters
        preview = prompt[:300] + "..." if len(prompt) > 300 else prompt
        print(f"Generated Prompt Preview:\n{preview}")
        
        # Check if topic-specific icons were used
        topic_icons = get_topic_specific_icons(topic.lower())
        if topic_icons:
            print(f"✅ Used topic-specific icons: {[icon['desc'] for icon in topic_icons[:3]]}")
        else:
            print(f"ℹ️  Used generic pattern for: {topic}")
        
        print("-" * 50)

    # Test the comma-separated functionality
    print(f"\n🎯 Multi-Topic Test")
    print("=" * 70)
    
    topics_input = "fitness, food, medicine"
    topics = [topic.strip() for topic in topics_input.split(',')]
    
    print(f"Input: {topics_input}")
    print(f"Parsed Topics: {topics}")
    print(f"Generated {len(topics)} prompts:")
    
    for i, topic in enumerate(topics, 1):
        prompt = generate_enhanced_icon_prompt(topic)
        print(f"\n{i}. {topic.title()}: {prompt[:100]}...")

if __name__ == "__main__":
    test_enhanced_icon_generation()
