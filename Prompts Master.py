import tkinter as tk
from tkinter import scrolledtext, messagebox, filedialog
from PIL import Image
from PIL import Image as PILImage  # Add this for compatibility with Resampling
import requests, base64, google.generativeai as genai, pyperclip
import time, sqlite3, csv, threading, os
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *
import re
import json
from pathlib import Path
from bs4 import BeautifulSoup  # Add this import for HTML parsing
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from ttkbootstrap.widgets import Meter  # Import Meter for progress bar

# Ensure compatibility with newer Pillow versions
try:
    Image.Resampling.BICUBIC
except AttributeError:
    Image.Resampling = Image  # Fallback for older Pillow versions

# Patch ttkbootstrap to use Image.Resampling.BICUBIC
from ttkbootstrap.widgets import Meter
from PIL import Image, ImageDraw, ImageTk

def patched_draw_meter(self, start_angle=None, extent=None, meter_color=None):
    """Patched method to fix the resampling issue in ttkbootstrap's Meter widget."""
    # Use default values if attributes are missing
    start_angle = start_angle if start_angle is not None else getattr(self, "_startangle", 0)
    extent = extent if extent is not None else getattr(self, "_extent", 360)
    meter_color = meter_color if meter_color is not None else getattr(self, "_metercolor", "blue")

    img = Image.new("RGBA", (self._metersize, self._metersize), (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    draw.pieslice(
        [(0, 0), (self._metersize, self._metersize)],
        start=start_angle,
        end=start_angle + extent,
        fill=meter_color,
    )
    self._meterimage = ImageTk.PhotoImage(img.resize((self._metersize, self._metersize), Image.Resampling.BICUBIC))

    # Directly set the image attribute without calling configure to avoid recursion
    self.image = self._meterimage

Meter._draw_meter = patched_draw_meter

APP_DATA_PATH = Path(os.getenv('APPDATA')) / "ImageToPrompt"
APP_DATA_PATH.mkdir(exist_ok=True)
API_KEY_FILE = APP_DATA_PATH / "api_key.json"

API_KEY = ""
local_images = []
monitoring = False

def save_api_key_to_file(api_key):
    with open(API_KEY_FILE, "w") as file:
        json.dump({"api_key": api_key}, file)

def load_api_key_from_file():
    if API_KEY_FILE.exists():
        with open(API_KEY_FILE, "r") as file:
            data = json.load(file)
            return data.get("api_key", "")
    return ""

def save_api_key():
    global API_KEY
    API_KEY = api_entry.get().strip()
    if not API_KEY:
        messagebox.showerror("Error", "API Key cannot be empty!")
        return
    genai.configure(api_key=API_KEY)
    save_api_key_to_file(API_KEY)
    log_activity("API key saved successfully.")
    messagebox.showinfo("API Key Saved", "API key saved successfully!")

# Load API key on startup
API_KEY = load_api_key_from_file()
if API_KEY:
    genai.configure(api_key=API_KEY)

def log_activity(activity):
    activity_log.insert(tk.END, f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {activity}\n")
    activity_log.yview(tk.END)

# Set to track processed URLs to prevent duplicates
processed_urls = set()

def init_db():
    """Initialize the database and clear previous session data."""
    global processed_urls
    processed_urls.clear()  # Clear the processed URLs set

    with sqlite3.connect("image_responses.db") as conn:
        cursor = conn.cursor()
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            image_url TEXT UNIQUE,
            description TEXT
        )
        """)
        # Clear previous session data
        cursor.execute("DELETE FROM responses")
        conn.commit()
    log_activity("Database initialized and cleared for the new session.")

def save_to_database(image_url, description):
    """Save a generated prompt to the database, avoiding duplicates."""
    global processed_urls

    # Check if URL has already been processed
    if image_url in processed_urls:
        log_activity(f"Skipping duplicate URL: {image_url}")
        return False

    try:
        with sqlite3.connect("image_responses.db") as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT OR IGNORE INTO responses (image_url, description) VALUES (?, ?)", (image_url, description))
            if cursor.rowcount > 0:  # Only add to set if actually inserted
                processed_urls.add(image_url)
                conn.commit()
                return True
            else:
                log_activity(f"URL already exists in database: {image_url}")
                return False
    except Exception as e:
        log_activity(f"Error saving to database: {e}")
        return False

def import_csv_links():
    """Import image links from a CSV file and process them."""
    file_path = filedialog.askopenfilename(
        title="Select CSV file with image links",
        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
    )

    if not file_path:
        return

    try:
        imported_links = []
        with open(file_path, "r", encoding="utf-8") as file:
            # Check if first line looks like headers
            first_line = file.readline().strip()
            file.seek(0)

            reader = csv.reader(file)

            # Skip header if it doesn't look like a URL
            if not first_line.startswith("http"):
                next(reader, None)  # Skip header row
            else:
                file.seek(0)  # Reset to beginning
                reader = csv.reader(file)

            for row_num, row in enumerate(reader, 1):
                if row:  # Skip empty rows
                    # Take the first column that contains a URL
                    url = None
                    for cell in row:
                        if cell and cell.strip().startswith("http"):
                            url = cell.strip()
                            break

                    if url:
                        imported_links.append(url)
                    else:
                        log_activity(f"Row {row_num}: No valid URL found")

        if not imported_links:
            messagebox.showwarning("Import", "No valid image URLs found in the CSV file.")
            return

        # Process the imported links
        log_activity(f"Found {len(imported_links)} URLs in CSV file. Starting processing...")

        def process_imported_links():
            successful = 0
            total = len(imported_links)

            for index, url in enumerate(imported_links, 1):
                try:
                    # Check for duplicates before processing
                    if url in processed_urls:
                        log_activity(f"Skipping duplicate URL: {url}")
                        continue

                    if process_image_url(url):
                        successful += 1

                    # Update progress
                    progress_label.config(text=f"Processing CSV: {index}/{total} (Success: {successful})")
                    root.update()

                    # Small delay to avoid overwhelming the API
                    time.sleep(1)

                except Exception as e:
                    log_activity(f"Error processing URL {index}: {e}")

            progress_label.config(text=f"CSV Import Complete: {successful}/{total} processed")
            log_activity(f"CSV import completed. Successfully processed {successful}/{total} URLs.")
            messagebox.showinfo("Import Complete", f"Successfully processed {successful} out of {total} URLs from CSV.")

        # Run processing in a separate thread
        threading.Thread(target=process_imported_links, daemon=True).start()

    except Exception as e:
        log_activity(f"Error importing CSV: {e}")
        messagebox.showerror("Import Error", f"Failed to import CSV file: {e}")

def export_to_csv():
    """Export only the prompts to a CSV file."""
    file_path = filedialog.asksaveasfilename(defaultextension=".csv", filetypes=[("CSV files", "*.csv")])
    if file_path:
        with sqlite3.connect("image_responses.db") as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT description FROM responses")  # Only fetch descriptions (prompts)
            rows = cursor.fetchall()

        if not rows:
            messagebox.showwarning("Export", "No data to export.")
            return

        # Write to CSV - only prompts
        with open(file_path, "w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            writer.writerow(["Prompt"])  # Single column header
            for row in rows:
                writer.writerow([row[0]])  # Only the prompt text

        log_activity(f"Exported {len(rows)} prompts to {file_path}")
        messagebox.showinfo("Export", f"Export completed successfully! {len(rows)} prompts exported.")

MAX_PROMPT_LENGTH = 1000  # Set a character limit for the prompt

def truncate_prompt_by_words(prompt, max_words):
    # Truncate the prompt to the specified maximum number of words
    words = prompt.split()
    if len(words) > max_words:
        return " ".join(words[:max_words]) + "..."
    return prompt

def generate_prompt(image_bytes, image_identifier):
    prompt_mode = mode_var.get()
    custom_prompt = custom_prompt_entry.get().strip()

    # Enhanced prompt templates with additional modes
    enhanced_prompts = {
        "Creative": (
            "You're an imaginative storyteller. Describe this image like it's a scene from a fantasy novel. "
            "Mention magical elements, surreal qualities, emotions, hidden meanings, and colors that evoke feelings. "
            "Avoid generic words like 'beautiful' or 'nice' – use poetic language."
        ),
        "Detailed": (
            "You are an expert visual analyst. Break down this image in high detail. And give me perfect prompt for AI image generation "
            "Include object types, their position and interaction, background, lighting (e.g., ambient, harsh), weather if visible, mood, perspective, and color harmony. "
            "Write it as if a 3D artist must recreate the exact scene."
        ),
        "Technical": (
            "Provide a professional breakdown for AI generation. Cover subject, background, textures, lighting setup (natural, spotlight, HDRi), tone (warm/cool), camera settings (depth, angle, focus), style (cyberpunk, photorealistic, sketch), and composition rules used (rule of thirds, symmetry)."
        ),
        "Marketing": (
            "Imagine this image is used in an advertisement. Describe it from a branding and marketing perspective. "
            "What story does it tell? Who is the target audience? What emotion does it invoke? Highlight visual cues that make it compelling."
        ),
        "MidJourney": (
            "Generate a MidJourney-style descriptive prompt with advanced modifiers. "
            "Include elements like lighting, depth of field, realism level, scene composition, and specific art styles. Format the prompt for direct use in MidJourney: --v 5 --ar 16:9 --style raw"
        ),
        "Ultra Descriptive": (
            "Generate a hyperrealistic, cinematic prompt for image generatino with precise visual storytelling. Give me a perfect prompt for this image in full detail, including: "
            "subject’s attire and physical posture, any hand or facial features with close-up precision, interactive objects and their texture, "
            "ambient or holographic digital elements, environment (blurred or sharp), realistic background cues, lighting type (soft natural, studio, diffused), "
            "depth of field focus points, and implied mood or atmosphere. Avoid generic terms. Use detailed, structured sentences. "
            "Ensure the scene is vivid enough to be recreated pixel-by-pixel. No special characters. Do not start the response with: 'Here's a description...'"
        ),
        "Isolated Object": (
            "Generate a hyper-detailed prompt of a single object placed on a clean, neutral background (e.g., pure white, gradient grey, or transparent). "
            "Describe the object's material, color, texture, shape, size, and any fine surface details (e.g., scratches, reflections, grain). "
            "Mention the lighting direction (soft shadow, front-lit, top-down), the object's orientation (angled, flat, upright), and how shadows or reflections fall. "
            "Do not include any background elements, human presence, or external props. This should be suitable for product photography, stock cutouts, or AI model training."
        )
    }

    # Use custom prompt if entered, otherwise select from predefined modes
    prompt_text = custom_prompt if custom_prompt else enhanced_prompts.get(prompt_mode, enhanced_prompts["Ultra Descriptive"])

    # Truncate if exceeds limit
    if len(prompt_text) > MAX_PROMPT_LENGTH:
        prompt_text = prompt_text[:MAX_PROMPT_LENGTH]
        log_activity(f"Prompt was truncated to {MAX_PROMPT_LENGTH} characters to fit within the limit.")

    try:
        model = genai.GenerativeModel("gemini-1.5-flash")
        gemini_response = model.generate_content([
            {"inline_data": {"mime_type": "image/jpeg", "data": image_bytes}},
            {"text": f"{prompt_text} Limit the response to {max_words_var.get()} words."}
        ])

        # Clean up Gemini's response
        description = gemini_response.text.strip()
        unwanted_intro_phrases = [
            "Here's a description of the image", "Here’s a description of the image",
            "This is a description of the image", "suitable for AI image generation:",
            "Image Description:", "Here's a structured description for AI image generation based on the image of",
            "The image is a", "This image shows a", "The image depicts a", "The image opens on a", "The scene unfolds in a",
        ]
        for phrase in unwanted_intro_phrases:
            if description.startswith(phrase):
                description = description[len(phrase):].strip()

        # Ensure the prompt starts with "Generate a"
        if not description.lower().startswith("generate a"):
            description = f"Generate a Image of {description}"

        # Format the description as a single paragraph
        description = " ".join(description.split())

        # Apply MidJourney formatting if enabled
        if mj_format_var.get():
            description = f"{description}, ultra-detailed, cinematic lighting, 4k, hyper-realistic --v 5 --ar 16:9"

        # Save and update UI only if not a duplicate
        if save_to_database(image_identifier, description):
            root.after(0, lambda: generated_data_tree.insert('', tk.END, values=(image_identifier, prompt_mode, description)))
            root.after(0, lambda: log_activity(f"{prompt_mode} mode processed: {image_identifier}"))
        else:
            root.after(0, lambda: log_activity(f"⚠ Skipped duplicate: {image_identifier}"))

    except Exception as e:
        root.after(0, lambda err=str(e): log_activity(f"Error generating prompt: {err}"))

def clean_description(text):
    # Remove special Unicode characters and extra whitespace
    return re.sub(r'[^\x00-\x7F]+', '', text).strip()

def process_local_image(image_path, retries=3):
    """Process a single local image with retry logic."""
    for attempt in range(retries):
        try:
            with open(image_path, "rb") as img_file:
                image_bytes = base64.b64encode(img_file.read()).decode("utf-8")
            generate_prompt(image_bytes, image_path)
            return True  # Success
        except Exception as e:
            log_activity(f"Attempt {attempt + 1} failed for {image_path}: {e}")
            time.sleep(1)  # Wait before retrying
    log_activity(f"Failed to process {image_path} after {retries} attempts.")
    return False  # Failure

def process_image_url(image_url, retries=3):
    """Process an image URL with retry logic."""
    for attempt in range(retries):
        try:
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()  # Raise an error for HTTP issues
            image_bytes = base64.b64encode(response.content).decode("utf-8")
            generate_prompt(image_bytes, image_url)
            return True  # Success
        except Exception as e:
            log_activity(f"Attempt {attempt + 1} failed for {image_url}: {e}")
            time.sleep(1)  # Wait before retrying
    log_activity(f"Failed to process {image_url} after {retries} attempts.")
    return False  # Failure

def monitor_clipboard():
    global monitoring, processed_urls
    last_clipboard = ""
    notified_urls = set()  # Track URLs we've already shown notifications for

    while monitoring:
        try:
            clipboard_content = pyperclip.paste()
            if (clipboard_content.startswith("http") and
                clipboard_content != last_clipboard):

                if clipboard_content not in processed_urls:
                    # Process new URL
                    process_image_url(clipboard_content)
                    last_clipboard = clipboard_content
                elif clipboard_content not in notified_urls:
                    # Show notification only once per URL
                    notified_urls.add(clipboard_content)
                    root.after(0, lambda: messagebox.showinfo("Duplicate URL", "This URL has already been processed in this session."))
                    last_clipboard = clipboard_content
                # If URL is in both processed_urls and notified_urls, do nothing (silent ignore)

        except Exception as e:
            log_activity(f"Clipboard monitoring error: {e}")
        time.sleep(2)

def start_monitoring():
    global monitoring
    monitoring = True
    threading.Thread(target=monitor_clipboard, daemon=True).start()
    log_activity("Clipboard monitoring started.")

    # Update button colors to show monitoring is active
    for widget in buttons_frame.winfo_children():
        if isinstance(widget, ttkb.Button) and "Start Monitoring" in widget.cget("text"):
            widget.configure(bootstyle="warning", text="🟡 Monitoring Active")
        elif isinstance(widget, ttkb.Button) and "Stop Monitoring" in widget.cget("text"):
            widget.configure(bootstyle="danger", text="⏹️ Stop Monitoring")

def stop_monitoring():
    global monitoring
    monitoring = False
    log_activity("Clipboard monitoring stopped.")

    # Reset button colors to show monitoring is inactive
    for widget in buttons_frame.winfo_children():
        if isinstance(widget, ttkb.Button) and "Monitoring Active" in widget.cget("text"):
            widget.configure(bootstyle="success", text="▶️ Start Monitoring")
        elif isinstance(widget, ttkb.Button) and "Stop Monitoring" in widget.cget("text"):
            widget.configure(bootstyle="secondary", text="⏹️ Stopped")

    messagebox.showinfo("Monitoring", "Clipboard monitoring stopped.")

# Always on top functionality
always_on_top_enabled = False

def toggle_always_on_top():
    global always_on_top_enabled
    always_on_top_enabled = not always_on_top_enabled
    root.attributes('-topmost', always_on_top_enabled)

    # Update button text and color
    for widget in buttons_frame.winfo_children():
        if isinstance(widget, ttkb.Button) and "Always On Top" in widget.cget("text"):
            if always_on_top_enabled:
                widget.configure(bootstyle="warning", text="📌 Always On Top (ON)")
                log_activity("Always on top enabled.")
            else:
                widget.configure(bootstyle="secondary", text="📌 Always On Top (OFF)")
                log_activity("Always on top disabled.")
            break

def clear_work():
    """Clear all work in the UI and database."""
    global processed_urls
    generated_data_tree.delete(*generated_data_tree.get_children())
    selected_files_list.delete(0, tk.END)
    processed_urls.clear()  # Clear processed URLs set

    with sqlite3.connect("image_responses.db") as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM responses")  # Clear current session data
        conn.commit()

    log_activity("Cleared all work and database.")

def select_local_images():
    global local_images
    local_images = filedialog.askopenfilenames(filetypes=[("Image Files", "*.jpg;*.jpeg;*.png")])
    selected_files_list.delete(0, tk.END)
    for file in local_images:
        selected_files_list.insert(tk.END, os.path.basename(file))
    log_activity("Local images selected.")

def start_processing_local_images():
    """Process all selected local images with progress tracking."""
    def process_all_images():
        total_images = len(local_images)
        log_activity(f"Starting to process {total_images} local images...")

        progress_label.config(text=f"Processing: 0/{total_images}")
        root.update()

        successful = 0
        for index, path in enumerate(local_images, start=1):
            if process_local_image(path):
                successful += 1
            progress_label.config(text=f"Processing: {index}/{total_images} (Success: {successful})")
            root.update()

        log_activity(f"Finished processing local images. Successfully processed {successful}/{total_images}.")
        progress_label.config(text=f"Completed: {successful}/{total_images}")

    threading.Thread(target=process_all_images, daemon=True).start()

def process_profile_images():
    """Process images from a profile page with retries and progress tracking."""
    url = profile_url_entry.get().strip()
    if not url.startswith("http"):
        messagebox.showerror("Invalid URL", "Please enter a valid profile URL.")
        return

    def fetch_and_process():
        try:
            response = requests.get(url, headers={'User-Agent': 'Mozilla/5.0'}, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, "html.parser")

            image_tags = soup.find_all("img")
            image_urls = list(set(tag.get("src") for tag in image_tags if tag.get("src") and "thumb" in tag.get("src")))

            total_images = len(image_urls)
            log_activity(f"Found {total_images} images. Starting prompt generation...")

            progress_label.config(text=f"Processing: 0/{total_images}")
            root.update()

            successful = 0
            for index, img_url in enumerate(image_urls, start=1):
                if process_image_url(img_url):
                    successful += 1
                progress_label.config(text=f"Processing: {index}/{total_images} (Success: {successful})")
                root.update()

            log_activity(f"Finished processing profile images. Successfully processed {successful}/{total_images}.")
            progress_label.config(text=f"Completed: {successful}/{total_images}")

        except Exception as e:
            log_activity(f"Error scraping profile: {e}")
            progress_label.config(text="Error: Failed to process profile")

    threading.Thread(target=fetch_and_process, daemon=True).start()

def extract_adobe_image_links(profile_url):
    headers = {"User-Agent": "Mozilla/5.0"}
    try:
        response = requests.get(profile_url, headers=headers)
        if response.status_code != 200:
            log_activity(f"Failed to fetch page. Status code: {response.status_code}")
            return []

        soup = BeautifulSoup(response.text, "html.parser")
        # Log the HTML content for debugging
        log_activity("Fetched page content successfully.")
        image_links = [img.get("src") for img in soup.find_all("img")
                       if img.get("src") and "ftcdn.net/jpg/" in img.get("src")]
        if not image_links:
            log_activity("No matching image links found on the page.")
        return list(set(image_links))
    except Exception as e:
        log_activity(f"Scrape error: {e}")
        return []

def get_adobe_image_links_with_chrome(profile_url):
    options = Options()
    options.add_argument("--headless")  # Show browser if you remove this
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")

    driver = webdriver.Chrome(ChromeDriverManager().install(), options=options)
    driver.get(profile_url)

    # Scroll to load more images (adjust if needed)
    for _ in range(20):  # Scroll multiple times
        driver.execute_script("window.scrollBy(0, 1000);")
        time.sleep(0.5)

    image_elements = driver.find_elements(By.TAG_NAME, "img")
    image_links = []
    for img in image_elements:
        src = img.get_attribute("src")
        if src and "ftcdn.net/jpg/" in src:
            image_links.append(src)

    driver.quit()
    return list(set(image_links))  # Unique URLs only

def get_adobe_image_links_with_scrolling(profile_url, scroll_count=20):
    options = Options()
    options.add_argument("--headless")  # Remove this line if you want to see the browser
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")

    driver = webdriver.Chrome(ChromeDriverManager().install(), options=options)
    driver.get(profile_url)

    print(f"Visiting: {profile_url}")
    time.sleep(2)

    # Scroll to load more images
    for i in range(scroll_count):
        driver.execute_script("window.scrollBy(0, 1500);")
        time.sleep(1.2)  # wait for new images to load

    # Get all image tags
    image_elements = driver.find_elements(By.TAG_NAME, "img")

    image_links = []
    for img in image_elements:
        src = img.get_attribute("src")
        if src and "ftcdn.net/jpg" in src and src.endswith(".jpg"):
            image_links.append(src)

    driver.quit()
    print(f"✅ Found {len(set(image_links))} image links.")
    return list(set(image_links))  # remove duplicates

def get_all_adobe_image_links(profile_url, scroll_times=30):
    options = Options()
    options.add_argument("--headless")  # comment this if you want to see browser
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")

    driver = webdriver.Chrome(ChromeDriverManager().install(), options=options)
    driver.get(profile_url)
    time.sleep(3)

    # Scroll to bottom multiple times to load images
    for _ in range(scroll_times):
        driver.execute_script("window.scrollBy(0, 1500);")
        time.sleep(1.5)

    # Adobe uses <picture> > <source> or <img>
    image_links = set()
    pictures = driver.find_elements(By.TAG_NAME, "picture")

    for pic in pictures:
        sources = pic.find_elements(By.TAG_NAME, "source")
        for src in sources:
            link = src.get_attribute("srcset")
            if link and "ftcdn.net" in link and link.endswith(".jpg"):
                image_links.add(link)

    # Backup: try <img> tags as fallback
    img_elements = driver.find_elements(By.TAG_NAME, "img")
    for img in img_elements:
        src = img.get_attribute("src")
        if src and "ftcdn.net" in src and src.endswith(".jpg"):
            image_links.add(src)

    driver.quit()
    print(f"✅ Found {len(image_links)} image links.")
    return list(image_links)

def process_adobe_profile():
    profile_url = profile_url_entry.get().strip()
    if not profile_url:
        messagebox.showerror("Missing URL", "Please enter a valid Adobe Stock contributor URL.")
        return

    def fetch_and_generate():
        try:
            # Try the more robust method with scrolling
            log_activity("Fetching Adobe Stock images with Chrome (this may take a minute)...")
            image_links = get_all_adobe_image_links(profile_url, scroll_times=30)

            if not image_links:
                log_activity("No image links found with Chrome. Trying alternative method...")
                image_links = extract_adobe_image_links(profile_url)

            if not image_links:
                log_activity("No image links found. Please check the URL or try a different profile.")
                progress_label.config(text="Error: No images found")
                return

            total_images = len(image_links)
            log_activity(f"Found {total_images} image links. Generating prompts...")

            progress_label.config(text=f"Processing: 0/{total_images}")
            root.update()

            successful = 0
            for index, img_url in enumerate(image_links, start=1):
                try:
                    # Check for duplicates before processing
                    if img_url in processed_urls:
                        log_activity(f"Skipping duplicate URL: {img_url}")
                        continue

                    response = requests.get(img_url)
                    image_bytes = base64.b64encode(response.content).decode("utf-8")
                    generate_prompt(image_bytes, img_url)
                    successful += 1

                    # Update progress
                    progress_label.config(text=f"Processing: {index}/{total_images} (Success: {successful})")
                    root.update()

                    # Slight delay to avoid overwhelming the API
                    time.sleep(1)
                except Exception as e:
                    log_activity(f"Failed processing image {index}/{total_images}: {img_url} — {e}")

            log_activity(f"Finished processing Adobe Stock images. Successfully processed {successful}/{total_images}.")
            progress_label.config(text=f"Completed: {successful}/{total_images}")

        except Exception as e:
            log_activity(f"Error processing Adobe Stock profile: {e}")
            progress_label.config(text="Error: Failed to process profile")

    threading.Thread(target=fetch_and_generate, daemon=True).start()

def add_profile_page_ui():
    profile_frame = ttkb.Frame(root)
    profile_frame.pack(fill=X, pady=5)

    ttkb.Label(profile_frame, text="Profile Page URL:").pack(side=LEFT, padx=5)
    global profile_url_entry
    profile_url_entry = ttkb.Entry(profile_frame, width=50)
    profile_url_entry.pack(side=LEFT, padx=5)

    process_profile_button = ttkb.Button(
        profile_frame, text="Process Profile Page",
        command=process_profile_images, bootstyle=PRIMARY
    )
    process_profile_button.pack(side=LEFT, padx=5)

    # Add Adobe Stock button
    process_adobe_button = ttkb.Button(
        profile_frame, text="Process Adobe Stock",
        command=process_adobe_profile, bootstyle=SUCCESS
    )
    process_adobe_button.pack(side=LEFT, padx=5)

# UI Setup
root = ttkb.Window(themename="darkly")  # Use ttkbootstrap Window and set a theme
root.title("Advanced Image Processor")
root.geometry("1200x700")

# Initialize max_words_var after root is created
max_words_var = tk.IntVar(value=100)  # Default to 100 words

# API Key Entry
api_frame = ttkb.Frame(root)
api_frame.pack(fill=X, pady=5)
api_entry = ttkb.Entry(api_frame, width=50)
api_entry.insert(0, API_KEY)  # Pre-fill the entry with the loaded API key
api_entry.pack(side=LEFT, padx=5)
save_api_button = ttkb.Button(api_frame, text="Save API Key", command=save_api_key, bootstyle=SUCCESS)
save_api_button.pack(side=LEFT, padx=5)

# Buttons and Mode selector
buttons_frame = ttkb.Frame(root)
buttons_frame.pack(fill=X, pady=5)

ttkb.Label(buttons_frame, text="Custom Prompt:").pack(side=LEFT, padx=5)
custom_prompt_entry = ttkb.Entry(buttons_frame, width=50)
custom_prompt_entry.pack(side=LEFT, padx=5)

mode_var = tk.StringVar(value="Detailed")
ttkb.Label(buttons_frame, text="Prompt Mode:").pack(side=LEFT, padx=5)
mode_menu = ttkb.Combobox(buttons_frame, textvariable=mode_var, values=["Creative", "Detailed", "Technical", "Marketing", "MidJourney", "Ultra Descriptive", "Isolated Object"], state="readonly")
mode_menu.pack(side=LEFT, padx=5)

mj_format_var = tk.BooleanVar(value=False)  # Add BooleanVar for MidJourney Format
ttkb.Checkbutton(buttons_frame, text="MidJourney Format", variable=mj_format_var, bootstyle=INFO).pack(side=LEFT, padx=5)

# Add a UI control for maximum word count
ttkb.Label(buttons_frame, text="Max Words:").pack(side=LEFT, padx=5)
max_words_spinbox = ttkb.Spinbox(buttons_frame, from_=10, to=1000, textvariable=max_words_var, width=5)
max_words_spinbox.pack(side=LEFT, padx=5)

btn_texts = ["Start Monitoring", "Stop Monitoring", "Import CSV Links", "Export to CSV", "Clear Work", "Select Local Images", "Start Processing Local Files", "📌 Always On Top (OFF)"]
btn_cmds = [start_monitoring, stop_monitoring, import_csv_links, export_to_csv, clear_work, select_local_images, start_processing_local_images, toggle_always_on_top]
btn_styles = [PRIMARY, DANGER, SUCCESS, INFO, WARNING, PRIMARY, SUCCESS, "secondary"]

for text, cmd, style in zip(btn_texts, btn_cmds, btn_styles):
    ttkb.Button(buttons_frame, text=text, command=cmd, bootstyle=style).pack(side=LEFT, padx=5)

# Add Profile Page UI
add_profile_page_ui()

# Paned Window for Selected Files and Generated Data
main_pane = ttkb.PanedWindow(root, orient=tk.HORIZONTAL)
main_pane.pack(fill=BOTH, expand=True, padx=10, pady=5)

selected_files_frame = ttkb.LabelFrame(main_pane, text="Selected Files", bootstyle=INFO)
selected_files_list = tk.Listbox(selected_files_frame)
selected_files_list.pack(fill=BOTH, expand=True, padx=5, pady=5)
main_pane.add(selected_files_frame, weight=1)

generated_data_frame = ttkb.LabelFrame(main_pane, text="Generated Data", bootstyle=INFO)
generated_data_tree = ttkb.Treeview(generated_data_frame, columns=("Filename", "Mode", "Description"), show="headings")
for col in ("Filename", "Mode", "Description"):
    generated_data_tree.heading(col, text=col)
generated_data_tree.pack(fill=BOTH, expand=True)
main_pane.add(generated_data_frame, weight=3)

# Progress Label
progress_frame = ttkb.Frame(root)
progress_frame.pack(fill=X, pady=5, padx=10)
progress_label = ttkb.Label(progress_frame, text="Ready", font=("Helvetica", 10))
progress_label.pack(side=LEFT, padx=5)

# Activity Log
activity_log = ttkb.ScrolledText(root, height=4)  # Removed bootstyle parameter
activity_log.pack(fill=BOTH, expand=False, padx=10, pady=5)

# Initialize the database after activity_log is defined
init_db()

root.mainloop()
