#!/usr/bin/env python3
"""
Test script for the comma-separated icon topics functionality
This tests the enhanced generate_custom_icon_set_prompt function
"""

def test_comma_separated_parsing():
    """Test the comma-separated topic parsing logic"""
    
    def parse_topics(topics_input):
        """Simulate the parsing logic from the main function"""
        topics = [topic.strip().strip("'\"") for topic in topics_input.split(',') if topic.strip()]
        return topics
    
    # Test cases
    test_inputs = [
        "'Insurance', 'Investment', 'Processing and Optimization', 'People', 'Speaking'",
        "Insurance, Investment, Processing and Optimization, People, Speaking",
        "Insurance,Investment,Processing and Optimization,People,Speaking",
        "'Insurance','Investment','Processing and Optimization','People','Speaking'",
        "\"Insurance\", \"Investment\", \"Processing and Optimization\", \"People\", \"Speaking\"",
        "Insurance",  # Single topic
        "Insurance, Investment",  # Two topics
        "",  # Empty input
        "   ,   ,   ",  # Only commas and spaces
    ]
    
    print("🔲 Comma-Separated Topics Parsing Test")
    print("=" * 60)
    
    for i, test_input in enumerate(test_inputs, 1):
        print(f"\n📋 Test Case {i}:")
        print(f"Input: '{test_input}'")
        
        topics = parse_topics(test_input)
        print(f"Parsed Topics: {topics}")
        print(f"Count: {len(topics)}")
        
        if topics:
            print("✅ Valid input")
            for j, topic in enumerate(topics, 1):
                print(f"   {j}. '{topic}'")
        else:
            print("❌ No valid topics found")
        
        print("-" * 40)

def test_prompt_generation_for_multiple_topics():
    """Test prompt generation for multiple topics"""
    
    def generate_icon_set_prompt_text(topic, icon_count, grid_type, icon_style):
        """Simplified version of the prompt generation function"""
        
        # Style descriptions
        style_descriptions = {
            "minimalist": "minimalist",
            "solid black": "solid black designs",
            "outlined": "outlined style with clean line work",
            "filled": "filled designs with solid colors",
            "gradient": "gradient-filled designs",
            "flat design": "flat design style",
            "line art": "line-drawn style",
            "detailed": "detailed designs with textures and shading"
        }
        
        style_desc = style_descriptions.get(icon_style, "minimalist")
        
        # Generate the prompt following the pattern
        if icon_count == "9":
            prompt = f"""A set of nine {style_desc} {topic} icons arranged in a {grid_type}. The top row features three solid designs – a primary {topic} symbol on the left, a secondary {topic} element in the middle, and a tertiary {topic} icon on the right. The middle row shows three variations – a detailed {topic} component on the left, a {topic} tool or device in the center, and a {topic} action or process on the right. The bottom row contains three line-drawn icons matching the top row's designs. All icons are rendered in a clean, modern, {style_desc} style against a white background, with balanced spacing and precise line work. The designs convey {topic} concepts with clarity and simplicity."""
        else:
            prompt = f"""A set of {icon_count} {style_desc} {topic} icons arranged in a {grid_type}. The collection comprehensively covers {topic} concepts, tools, processes, and applications. All icons feature consistent {style_desc} design with uniform styling, balanced proportions, and professional quality. Clean white background with precise spacing and modern aesthetic that conveys {topic} concepts with clarity and simplicity."""
        
        return prompt
    
    # Test with your example input
    topics_input = "'Insurance', 'Investment', 'Processing and Optimization', 'People', 'Speaking'"
    topics = [topic.strip().strip("'\"") for topic in topics_input.split(',') if topic.strip()]
    
    icon_count = "9"
    grid_type = "3x3 grid"
    icon_style = "minimalist"
    
    print("\n🎯 Multiple Topics Prompt Generation Test")
    print("=" * 60)
    print(f"Input: {topics_input}")
    print(f"Settings: {icon_count} icons, {grid_type}, {icon_style}")
    print("=" * 60)
    
    for i, topic in enumerate(topics, 1):
        print(f"\n📋 Prompt {i}/{len(topics)}: {topic}")
        print("-" * 40)
        
        prompt = generate_icon_set_prompt_text(topic, icon_count, grid_type, icon_style)
        
        # Show first 200 characters of the prompt
        preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
        print(f"Generated Prompt Preview:\n{preview}")
        print("-" * 40)
    
    print(f"\n✅ Successfully generated {len(topics)} icon set prompts!")

if __name__ == "__main__":
    test_comma_separated_parsing()
    test_prompt_generation_for_multiple_topics()
